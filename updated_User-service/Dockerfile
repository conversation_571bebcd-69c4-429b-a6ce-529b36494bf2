# FROM node:20
# # Create app directory
# WORKDIR /src
# # Install app dependencies
# # A wildcard is used to ensure both package.json AND package-lock.json are copied
# # where available (npm@5+)
# COPY package*.json ./
# RUN npm install 
# COPY . .
# RUN npm run build 
# EXPOSE 3000
# CMD [ "npm", "run", "start:prod" ]


    # Stage 1: Build the application
    FROM node:${NODE_VERSION} AS build
WORKDIR /src
COPY package*.json ./
RUN npm install
COPY . . 
RUN npm run build

# Stage 2: Create production image
FROM node:20 AS production
WORKDIR /app
COPY --from=build /src/dist ./dist
COPY --from=build /src/node_modules ./node_modules
COPY --from=build /src/package.json ./
EXPOSE 3000
CMD ["npm", "run", "start:prod"]


# FROM node:18-alpine

# # Install bash
# RUN apk --no-cache add bash

# # Create app directory
# WORKDIR /src

# # Install app dependencies
# COPY package*.json ./
# RUN npm install 

# # Copy the rest of the application code
# COPY . .

# # Build the application
# RUN npm run build 

# # Expose port 3000
# EXPOSE 3000

# # Start the application in production mode
# CMD [ "npm", "run", "start:prod" ]


# Stage 1: Build
# FROM node:18-alpine AS build
# WORKDIR /src
# COPY package*.json ./
# RUN npm install --location=global ts-node
# RUN npm install
# COPY . .
# RUN npm run build

# # Stage 2: stage
# FROM node:18-alpine
# WORKDIR /src
# COPY --from=build /src/dist ./dist
# COPY --from=build /src/node_modules ./node_modules
# COPY --from=build /src/package.json ./
# EXPOSE 3000
# CMD ["npm", "run", "start:prod"]

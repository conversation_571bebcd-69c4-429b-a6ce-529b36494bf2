import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BlockNumber } from './entities/blockNumber.entity';
import { BlockNumberRepository } from './blockNumber.repository';
import { EventListenerService } from './eventHandler.service';
import { Transaction } from 'src/transaction/entity/transaction.entity';
import { Application } from 'src/application/entities/application.entity';
import { AppRelayer } from 'src/appRelayer/entities/appRelayer.entity';
import { PodCoordinatorModule } from '../pod-coordinator/pod-coordinator.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      BlockNumber,
      Transaction,
      Application,
      AppRelayer,
    ]),
    PodCoordinatorModule,
  ],
  providers: [BlockNumberRepository, EventListenerService],
  exports: [BlockNumberRepository, EventListenerService],
})
export class EventHandlingModule {}

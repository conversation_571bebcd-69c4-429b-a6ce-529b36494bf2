import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BlockNumber } from './entities/blockNumber.entity';

@Injectable()
export class BlockNumberRepository {
  constructor(
    @InjectRepository(BlockNumber)
    private readonly eventRepository: Repository<BlockNumber>,
  ) {}

  async getLastProcessedBlock(chainId: number): Promise<number> {
    const event = await this.eventRepository.findOne({ where: { chainId } });
    return event ? event.lastProcessedBlock : 0;
  }

  async updateLastProcessedBlock(
    chainId: number,
    blockNumber: number,
  ): Promise<void> {
    let event = await this.eventRepository.findOne({ where: { chainId } });

    if (event) {
      event.lastProcessedBlock = blockNumber;
      await this.eventRepository.save(event);
    } else {
      event = this.eventRepository.create({
        chainId,
        lastProcessedBlock: blockNumber,
      });
      await this.eventRepository.save(event);
    }
  }
}

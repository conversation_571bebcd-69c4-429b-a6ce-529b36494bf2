import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PodCoordinator } from './entities/pod-coordinator.entity';

@Injectable()
export class PodCoordinatorService {
  private readonly logger = new Logger(PodCoordinatorService.name);
  private readonly LEADER_KEY = 'LEADER_POD';
  private isLeader = false;
  private leadershipChangeCallbacks: ((isLeader: boolean) => void)[] = [];
  private readonly hostname: string;

  constructor(
    @InjectRepository(PodCoordinator)
    private readonly podCoordinatorRepository: Repository<PodCoordinator>,
  ) {
    // Generate a fallback hostname if not available
    this.hostname =
      process.env.HOSTNAME ||
      process.env.NODE_NAME ||
      `pod-${Math.random().toString(36).substring(7)}-${Date.now()}`;

    this.logger.log(`Pod initialized with hostname: ${this.hostname}`);
    this.initializeLeaderElection();
  }

  private async initializeLeaderElection() {
    // Try to become leader immediately
    await this.tryBecomeLeader();

    // Set up periodic leader election check
    setInterval(async () => {
      await this.tryBecomeLeader();
    }, 10000); // Check every 10 seconds
  }

  private async tryBecomeLeader() {
    try {
      const currentTime = new Date();

      // Try to find existing leader
      let leader = await this.podCoordinatorRepository.findOne({
        where: { key: this.LEADER_KEY },
      });

      if (!leader) {
        // No leader exists, try to become leader
        leader = this.podCoordinatorRepository.create({
          key: this.LEADER_KEY,
          leaderPodName: this.hostname,
          lastHeartbeat: currentTime,
        });
      } else {
        // Check if current leader is still alive
        const heartbeatThreshold = new Date(
          currentTime.getTime() - leader.ttlSeconds * 1000,
        );

        if (leader.lastHeartbeat < heartbeatThreshold) {
          // Previous leader is dead, take over
          leader.leaderPodName = this.hostname;
          leader.lastHeartbeat = currentTime;
        } else if (leader.leaderPodName === this.hostname) {
          // We are the current leader, update heartbeat
          leader.lastHeartbeat = currentTime;
        }
      }

      // Save changes
      await this.podCoordinatorRepository.save(leader);

      // Update local leader status and notify callbacks if changed
      const wasLeader = this.isLeader;
      this.isLeader = leader.leaderPodName === this.hostname;

      if (wasLeader !== this.isLeader) {
        this.logger.log(
          `Pod ${this.hostname} ${this.isLeader ? 'became leader' : 'is no longer leader'}`,
        );
        // Notify all registered callbacks about the leadership change
        for (const callback of this.leadershipChangeCallbacks) {
          callback(this.isLeader);
        }
      }
    } catch (error) {
      this.logger.error('Error in leader election:', error);
    }
  }

  public isLeaderPod(): boolean {
    return this.isLeader;
  }

  public onLeadershipChange(callback: (isLeader: boolean) => void): void {
    this.leadershipChangeCallbacks.push(callback);
    // Immediately call with current status
    callback(this.isLeader);
  }
}

import {
  Body,
  Controller,
  Delete,
  Get,
  Headers,
  Patch,
  Post,
  Query,
  Req,
  Res,
  UseGuards,
  Logger,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { Response } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { ApiBearerAuth, ApiTags, ApiBody, ApiConsumes } from '@nestjs/swagger';
import { ChatAiService } from './chatAi.service';
import {
  CreateChatAiDto,
  UpdateChatAiDto,
  CreateDocumentDto,
  UpdateDocumentDto,
  CreateChatMessageDto,
  ChatQueryDto,
  FetchSingleChatAiDto,
  FetchChatAisDto,
  FetchSingleDocumentDto,
  FetchDocumentsDto,
  RemoveChatAiDto,
  UpdateChatAiSettingDto,
  RemoveDocumentDto,
} from './dto/chatAi.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { User } from '../user/entities/user.entity';

@ApiTags('ChatAI')
@Controller('users/app/chatai')
export class ChatAiController {
  private readonly logger = new Logger(ChatAiController.name);

  constructor(private readonly chatAiService: ChatAiService) {}

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('setup-chatai')
  async setupChatAi(
    @Req() req: { user: User },
    @Body() createChatAiDto: CreateChatAiDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.setupChatAi(userId, createChatAiDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Patch('update-chatai')
  async updateChatAi(
    @Req() req: { user: User },
    @Body() updateChatAiDto: UpdateChatAiDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.updateChatAi(userId, updateChatAiDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Patch('update-settings')
  async updateChatAiSetting(
    @Req() req: { user: User },
    @Body() updateChatAiSettingDto: UpdateChatAiSettingDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.updateChatAiSettings(
      userId,
      updateChatAiSettingDto,
    );
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-single-chatai')
  async getSingleChatAi(
    @Req() req: { user: User },
    @Query() query: FetchSingleChatAiDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.getSingleChatAi(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-all-chatais')
  async getAllChatAis(
    @Req() req: { user: User },
    @Query() query: FetchChatAisDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.getAllChatAis(userId, query);
  }

  // @UseGuards(JwtAuthGuard)
  // @ApiBearerAuth()
  // @Get('get-transactions')
  // async getTransactions(
  //   @Query() query: GetChatAiTransactionDto,
  //   @Req() req: { user: User },
  // ) {
  //   const userId = req.user.id;
  //   return await this.chatAiService.getAllTransactions(userId, query);
  // }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Delete('remove-chatai')
  async removeChatAi(
    @Req() req: { user: User },
    @Body() removeChatAiDto: RemoveChatAiDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.removeChatAi(userId, removeChatAiDto);
  }

  // ==================== Document Management ====================

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('upload-document')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        appId: {
          type: 'string',
          description: 'Application ID',
        },
        title: {
          type: 'string',
          description: 'Document title',
        },
        description: {
          type: 'string',
          description: 'Document description',
        },
      },
      required: ['file', 'appId'],
    },
  })
  @UseInterceptors(
    FileInterceptor('file', {
      limits: {
        fileSize: 20 * 1024 * 1024, // 20MB limit
      },
    }),
  )
  async uploadDocument(
    @UploadedFile() file: Express.Multer.File,
    @Body() createDocumentDto: CreateDocumentDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.chatAiService.uploadDocument(
      userId,
      file,
      createDocumentDto,
    );
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-documents')
  async getDocuments(
    @Query() query: FetchDocumentsDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.chatAiService.getDocuments(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-document')
  async getSingleDocument(
    @Query() query: FetchSingleDocumentDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.chatAiService.getSingleDocument(userId, query);
  }

  // ==================== Internal APIs (for ChatAI-SDK-Clean) ====================

  /**
   * Internal API for ChatAI-SDK-Clean to update document status
   * No authentication required - internal service communication
   */
  @Post('internal/update-document-status')
  async updateDocumentStatus(
    @Body()
    updateData: {
      appId: string;
      documentId: string;
      status: string;
      message?: string;
      parsedData?: any;
      pageCount?: number;
      wordCount?: number;
      indexId?: string;
    },
    @Headers() headers: any,
  ) {
    // Verify internal API key
    const expectedKey = process.env.INTERNAL_API_KEY || 'chatai-internal-2024';
    const internalApiKey = headers['x-internal-api-key'];
    if (internalApiKey !== expectedKey) {
      return {
        error: true,
        statusCode: 401,
        message: 'Invalid internal API key',
      };
    }

    return await this.chatAiService.updateDocumentStatusInternal(updateData);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Patch('update-document')
  async updateDocument(
    @Req() req: { user: User },
    @Body() updateDocumentDto: UpdateDocumentDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.updateDocument(userId, updateDocumentDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Delete('remove-document')
  async removeDocument(
    @Req() req: { user: User },
    @Body() removeDocumentDto: RemoveDocumentDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.removeDocument(userId, removeDocumentDto);
  }

  // ==================== Chat Management ====================
  // Note: Chat functionality has been moved to ChatAI-SDK service
  // This service now only handles document management and app validation

  // ==================== Credit Management ====================
  // Note: Credit management has been moved to key-validator API optimization
}

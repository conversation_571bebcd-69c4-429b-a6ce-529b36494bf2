import { Module, forwardRef } from '@nestjs/common';
import { AppRelayerService } from './appRelayer.service';
import { AppRelayerController } from './appRelayer.controller';
import { AuthModule } from '../auth/auth.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../user/entities/user.entity';
import { AppRelayer } from './entities/appRelayer.entity';
import { GatewayModule } from '../gateway/gateway.module';
import { TransactionModule } from '../transaction/transaction.module';
import { ConfigModule } from '@nestjs/config';
import { AbiService } from '../utils/abi.service';
import { Application } from '../application/entities/application.entity';
import { OriginsModule } from '../origins/origins.module';
import { RelayersTransaction } from './entities/transaction.entity';
import { PodCoordinatorModule } from '../pod-coordinator/pod-coordinator.module';
import { ClientsModule, Transport } from '@nestjs/microservices';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    forwardRef(() => AuthModule),
    TypeOrmModule.forFeature([
      Application,
      AppRelayer,
      User,
      RelayersTransaction,
    ]),
    ClientsModule.register([
      {
        name: 'APP_RELAYER_SERVICE',
        transport: Transport.RMQ,
        options: {
          urls: [process.env.RMQ_URL],
          queue: 'update_app_relayer_q',
          queueOptions: {
            durable: true,
            autoDelete: true,
          },
          noAck: false,
          prefetchCount: 1,
        },
      },
    ]),
    GatewayModule,
    TransactionModule,
    OriginsModule,
    PodCoordinatorModule,
  ],
  providers: [AppRelayerService, AbiService],
  controllers: [AppRelayerController],
  exports: [AppRelayerService, AbiService],
})
export class AppRelayerModule {}

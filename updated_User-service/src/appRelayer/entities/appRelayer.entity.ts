import { Application } from 'src/application/entities/application.entity';
import {
  <PERSON>tity,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  Join<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
} from 'typeorm';

@Entity()
export class AppRelayer {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: false })
  relayerName: string;

  @Column({
    default: 0,
    type: 'decimal',
    precision: 30,
    scale: 18,
    transformer: {
      to(value) {
        return value;
      },
      from(value) {
        return value;
      },
    },
  })
  balance: string;

  @Column({
    default: 0,
    type: 'decimal',
    precision: 30,
    scale: 18,
    transformer: {
      to(value) {
        return value;
      },
      from(value: string) {
        return value?.toString();
      },
    },
  })
  blockedBalance: string;

  @Column({
    default: 0,
    type: 'decimal',
    precision: 30,
    scale: 18,
    transformer: {
      to(value) {
        return value;
      },
      from(value: string) {
        return value?.toString();
      },
    },
  })
  threshold: string;

  @Column({ nullable: false, default: true })
  isActive: boolean;

  @Column({ nullable: false, default: false })
  notificationsEnabled: boolean;

  @Column({ nullable: true })
  notificationEmail: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  lastNotifiedAt: Date;

  @OneToOne(() => Application, (app) => app.relayer, {
    cascade: ['insert', 'update', 'remove'], // Cascades on insert, update, and remove
    onDelete: 'CASCADE', // Cascades delete operations
  })
  @JoinColumn()
  app: Application;
}

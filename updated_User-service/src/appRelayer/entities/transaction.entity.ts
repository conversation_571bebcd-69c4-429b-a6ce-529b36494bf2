import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
export class Relayers {
  @PrimaryGeneratedColumn('uuid')
  id: string;
}

@Entity()
export class RelayersTransaction {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  hash: string;

  @Column({ nullable: true })
  type: number;

  @Column({ nullable: true })
  blockHash: string;

  @Column({ nullable: true })
  blockNumber: number;

  @Column({ nullable: true })
  confirmations: number;

  @Column({ nullable: true })
  from: string;

  @Column({ nullable: true })
  gasPrice: string;

  @Column({ nullable: true })
  maxPriorityFeePerGas: string;

  @Column({ nullable: true })
  maxFeePerGas: string;

  @Column({ nullable: true })
  gasLimit: string;

  @Column({ nullable: true })
  to: string;

  @Column({
    default: 0,
    type: 'decimal',
    precision: 30,
    scale: 4,
  })
  value: string;

  @Column({ nullable: true })
  nonce: number;

  @Column({ nullable: true })
  data: string;

  @Column({ nullable: true })
  chainId: number;

  @Column({
    type: 'text',
    default: 'deposit',
    // enum: ['deposit', 'withdraw'],
  })
  txType: string;

  @Column({
    type: 'enum',
    default: 'pending',
    enum: ['initiated', 'pending', 'confirmed', 'failed', 'rejected'],
  })
  status: string;

  @Column({
    type: 'text',
    nullable: true,
    transformer: {
      to(value) {
        return JSON.stringify(value);
      },
      from(value) {
        return JSON.parse(value);
      },
    },
  })
  receipt: string;

  @Column({
    type: 'enum',
    default: 'relayer',
    enum: ['paymaster', 'bundler', 'relayer'],
  })
  service: string;

  @Column({ type: 'uuid', nullable: true })
  relayerId: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;

  @Column({
    type: 'text',
    nullable: true,
    transformer: {
      to(value) {
        return JSON.stringify(value);
      },
      from(value) {
        return value ? JSON.parse(value) : {};
      },
    },
  })
  transactionDetail: string;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  expiryAt: Date;

  @Column({ nullable: false, default: 0 })
  retryCount: number;

  @Column({ type: 'uuid', nullable: true })
  appRelayerId: string;

  @Column({
    default: 0,
    type: 'decimal',
    precision: 30,
    scale: 18,
  })
  estimatedGas: string;

  @Column({
    default: 0,
    type: 'decimal',
    precision: 30,
    scale: 18,
  })
  actualGas: string;

  @Column({
    type: 'text',
    nullable: true,
    transformer: {
      to(value) {
        return JSON.stringify(value);
      },
      from(value) {
        return value;
      },
    },
  })
  reason: string;
}

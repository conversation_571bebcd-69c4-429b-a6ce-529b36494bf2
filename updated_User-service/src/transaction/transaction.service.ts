import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { QueryRunner, Repository } from 'typeorm';

import {
  CommonMessage,
  TransactionMessage,
} from '../CommonMessages/CommonMessages';
import { Transaction } from './entity/transaction.entity';

@Injectable()
export class TransactionService {
  private readonly logger = new Logger(TransactionService.name, {
    timestamp: true,
  });

  constructor(
    @InjectRepository(Transaction)
    private readonly transactionRepository: Repository<Transaction>,
  ) {}

  async updateUserTransaction(
    userTransaction: Transaction,
    transaction: QueryRunner,
  ) {
    try {
      const createAdminTransaction =
        await transaction.manager.save(userTransaction);
      if (!createAdminTransaction) {
        await transaction.rollbackTransaction();
        this.logger.verbose(
          'rolled back the query runner for updateUserTransaction when failed to create admin transaction record',
        );
        return {
          error: true,
          message: TransactionMessage.UPDATE_ERROR,
        };
      }
      return {
        error: false,
        message: TransactionMessage.UPDATE_SUCCESS,
      };
    } catch (error) {
      await transaction.rollbackTransaction();
      this.logger.verbose(
        'rolled back the query runner for updateUserTransaction when in catch at updateUserTransaction ',
      );
      this.logger.debug(
        'Error at catch when updateUserTransaction failed!',
        error.stack,
      );
      return { error: true, message: CommonMessage.InternalError };
    }
  }

  async getUserTransactionByQuery(query: unknown) {
    try {
      const doesTokenExist = await this.transactionRepository.findOne(query);
      if (!doesTokenExist) {
        return {
          error: true,
          message: TransactionMessage.NOT_FOUND,
          data: null,
        };
      }
      return {
        error: false,
        message: TransactionMessage.FOUND,
        data: doesTokenExist,
      };
    } catch (error) {
      return { error: true, message: CommonMessage.InternalError, data: null };
    }
  }
}

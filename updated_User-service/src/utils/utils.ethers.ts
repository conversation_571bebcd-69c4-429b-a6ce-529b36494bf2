import { AbiCoder, Contract, ethers, JsonRpcProvider } from 'ethers';
import { RPC_PROVIDER_URLS, ChainId } from './utils.networks';
import { Logger } from '@nestjs/common';
import { slice } from 'viem';
import { AppRelayer } from 'src/appRelayer/entities/appRelayer.entity';
import { DEFAULT_ABI } from './default.abi';
const logger = new Logger('EthersUtils', { timestamp: true });

export const createProvider = async (chainName: string) => {
  return new JsonRpcProvider(RPC_PROVIDER_URLS[ChainId[chainName]]);
};

export const withdrawForDecoder = async (transaction: any) => {
  let uint256ValInNumber = '0';
  if (transaction && transaction.data) {
    const abiCoder = AbiCoder.defaultAbiCoder();
    const decodedData = abiCoder.decode(
      ['address', 'uint256'],
      slice(transaction.data, 4),
    );
    const uint256Value = decodedData[1];
    uint256ValInNumber = uint256Value ? uint256Value.toString() : '0';
  }
  return uint256ValInNumber;
};

export async function validateChainId(chainId: number): Promise<string> {
  const validChainIds = Object.keys(ChainId).find(
    (key) => ChainId[key] === Number(chainId),
  );
  return validChainIds;
}

export const initializeProvider = async (chainId: number) => {
  try {
    const chainName = await validateChainId(chainId);
    if (!chainName) {
      return null;
    }
    const chainProvider = await createProvider(chainName);
    if (!chainProvider) {
      return null;
    }
    return { chainProvider, chainName };
  } catch (error) {
    logger.error('Error in catch at initializeProvider', JSON.stringify(error));
    return null;
  }
};

export const getTransactionByHash = async ({
  chainId,
  hash,
}: {
  chainId: number;
  hash: string;
}) => {
  try {
    const provider = await initializeProvider(chainId);
    if (!provider) {
      return { error: true, transaction: null };
    }
    const transaction = await provider.chainProvider.getTransaction(hash);
    return { error: false, transaction: transaction };
  } catch (error) {
    logger.error(
      'error in catch at getTransactionByHash',
      JSON.stringify(error),
    );
    return { error: true, transaction: null };
  }
};

export function isValidContractAddress(address) {
  // Check if the address is a string and has a length of 42 characters (for Ethereum addresses)
  if (typeof address !== 'string' || address.length !== 42) {
    return false;
  }

  // Check if the address starts with '0x' (common for Ethereum addresses)
  if (!address.startsWith('0x')) {
    return false;
  }

  // Check if the rest of the characters are valid hexadecimal
  const hexRegex = /^[0-9a-fA-F]+$/;
  if (!hexRegex.test(address.slice(2))) {
    return false;
  }
  // If all checks pass, return true
  return true;
}

export const handleValidateUserBalance = async (
  appData: AppRelayer,
  payload: any,
  data: any,
) => {
  try {
    const provider = await initializeProvider(appData.app.chainId);

    // Find the contract in the app's smart contracts list
    const matchedContract = appData.app.smartContracts.find(
      (c) =>
        c.address.toLowerCase() ===
        payload?.params?.contractAddress.toLowerCase(),
    );

    if (!matchedContract) {
      throw new Error('Matching contract not found');
    }

    const abi = matchedContract.abi;
    const contract: Contract = new ethers.Contract(
      payload?.params?.contractAddress,
      abi,
      provider.chainProvider,
    );

    const feeData = await provider.chainProvider.getFeeData();

    const gasPrice = feeData.gasPrice || ethers.parseUnits('5', 'gwei');

    const gasEstimate = await contract.executeMetaTransaction.estimateGas(
      ...data,
    );

    const estimatedGas = ethers.toBigInt(gasEstimate);

    const totalTxCost = estimatedGas * gasPrice;

    const relayerBalanceInWei = ethers.parseUnits(
      appData?.balance.toString(),
      18,
    );

    const relayerBlockedBalanceInWei = ethers.parseUnits(
      appData?.blockedBalance.toString(),
      18,
    );

    if (totalTxCost > relayerBalanceInWei - relayerBlockedBalanceInWei) {
      return {
        error: true,
        statusCode: 400,
        message: 'Insufficient relayer balance',
        result: null,
      };
    }

    return {
      error: false,
      statusCode: 200,
      message: 'Sufficient relayer balance',
      estimateGas: estimatedGas.toString(),
      gasPrice: gasPrice.toString(),
      totalCost: totalTxCost.toString(),
    };
  } catch (error) {
    console.error('Error in handleValidateUserBalance:', error);
    return {
      error: true,
      statusCode: 500,
      message: 'Internal server error while validating balance',
    };
  }
};

export const handleValidateUserBalanceV2 = async (
  appData: AppRelayer,
  payload: any,
  data: any,
) => {
  try {
    const provider = await initializeProvider(appData.app.chainId);

    // Find the contract in the app's smart contracts list
    const contract: Contract = new ethers.Contract(
      payload?.params?.contractAddress,
      DEFAULT_ABI,
      provider.chainProvider,
    );

    const feeData = await provider.chainProvider.getFeeData();

    const gasPrice = feeData.gasPrice || ethers.parseUnits('5', 'gwei');

    const gasEstimate = await contract.executeMetaTransaction.estimateGas(
      ...data,
    );

    const estimatedGas = ethers.toBigInt(gasEstimate);

    const totalTxCost = estimatedGas * gasPrice;

    const relayerBalanceInWei = ethers.parseUnits(
      appData?.balance.toString(),
      18,
    );

    const relayerBlockedBalanceInWei = ethers.parseUnits(
      appData?.blockedBalance.toString(),
      18,
    );

    if (totalTxCost > relayerBalanceInWei - relayerBlockedBalanceInWei) {
      return {
        error: true,
        statusCode: 400,
        message: 'Insufficient relayer balance',
        result: null,
      };
    }

    return {
      error: false,
      statusCode: 200,
      message: 'Sufficient relayer balance',
      estimateGas: estimatedGas.toString(),
      gasPrice: feeData.gasPrice ? feeData.gasPrice.toString() : '0',
    };
  } catch (error: any) {
    console.error('Error in handleValidateUserBalance:', error);

    // Default message
    let errorMessage = 'Internal server error while validating balance';

    // Check for known ethers error shape
    if (error?.reason) {
      errorMessage = error.reason;
    } else if (error?.revert?.args?.[0]) {
      errorMessage = error.revert.args[0];
    } else if (error?.error?.message) {
      errorMessage = error.error.message;
    } else if (error?.message) {
      errorMessage = error.message;
    }

    return {
      error: true,
      statusCode: 500,
      message: errorMessage,
    };
  }
};

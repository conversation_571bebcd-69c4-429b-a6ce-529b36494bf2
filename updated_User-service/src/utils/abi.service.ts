import { HttpStatus, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { CommonMessage } from 'src/CommonMessages/CommonMessages';

@Injectable()
export class AbiService {
  constructor(private configService: ConfigService) {}

  private readonly BASE_URL = 'https://api.etherscan.io/v2/api';

  async getAbi(chainId: number, contractAddress: string) {
    try {
      const apiKey = this.configService.get<string>('ETHERSCAN'); // One API key used for all
      const apiUrl = this.getApiUrl(
        chainId,
        contractAddress,
        'getsourcecode',
        apiKey,
      );
      const response = await axios.get(apiUrl);

      if (!response.data || response.data.status !== '1') {
        return this.handleError(
          HttpStatus.INTERNAL_SERVER_ERROR,
          CommonMessage.InternalError,
        );
      }

      const abiResult = response.data.result[0];
      if (abiResult['ABI'] === 'Contract source code not verified') {
        return this.handleError(HttpStatus.BAD_REQUEST, abiResult['ABI']);
      }

      let abi: any[];
      if (abiResult['Proxy'] === '1' && abiResult['Implementation']) {
        const proxyApiUrl = this.getApiUrl(
          chainId,
          abiResult['Implementation'],
          'getabi',
          apiKey,
        );
        const proxyResponse = await axios.get(proxyApiUrl);

        if (!proxyResponse.data || proxyResponse.data.status === '0') {
          return this.handleError(
            HttpStatus.BAD_REQUEST,
            proxyResponse.data.message,
          );
        }

        abi = JSON.parse(proxyResponse.data.result);
      } else {
        abi = JSON.parse(abiResult['ABI']);
      }

      const nonpayableFunctions = this.filterNonPayableFunctions(abi);
      const functionNames = nonpayableFunctions.map((item: any) => item.name);

      const data = {
        methods: functionNames,
        methodCount: functionNames.length,
        abi: JSON.stringify(abi),
      };

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: CommonMessage.AbiFound,
        result: data,
      };
    } catch (error) {
      return this.handleError(
        HttpStatus.INTERNAL_SERVER_ERROR,
        CommonMessage.InternalError,
      );
    }
  }

  private getApiUrl(
    chainId: number,
    contractAddress: string,
    action: string,
    apiKey: string,
  ) {
    return `${this.BASE_URL}?chainid=${chainId}&module=contract&action=${action}&address=${contractAddress}&apikey=${apiKey}`;
  }

  private filterNonPayableFunctions(abi: any[]): any[] {
    return abi.filter((item: any) => {
      return (
        (item.stateMutability === 'nonpayable' ||
          item.stateMutability === 'payable') &&
        item.type === 'function'
      );
    });
  }

  validateAbiFunctions(abi: any[], methods: any[]) {
    const nonpayableFunctions = this.filterNonPayableFunctions(abi);
    const functionsList = nonpayableFunctions.map((item: any) => item.name);
    return methods.every((item) => functionsList.includes(item));
  }

  private handleError(statusCode: HttpStatus, message: string) {
    return {
      error: true,
      statusCode,
      message,
    };
  }
}

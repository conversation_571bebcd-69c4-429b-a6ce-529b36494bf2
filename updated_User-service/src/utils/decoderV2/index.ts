import { ContractResponse } from './types';
import { Hex } from 'viem';
import { Interface } from 'readline';

export const apiBasePath = 'https://swiss-knife.xyz';

export const fetchContractAbiRaw = async ({
  address,
  chainId,
}: {
  address: string;
  chainId: number;
}): Promise<{
  abi: Interface;
  name: string;
  implementation?: {
    address: string;
    abi: Interface;
    name: string;
  };
}> => {
  const res = await fetch(
    `${apiBasePath}/api/source-code?address=${address}&chainId=${chainId}`,
  );

  const data: ContractResponse = await res.json();
  const { ABI, ContractName, Implementation } = data.result[0];

  if (Implementation.length > 0) {
    const res = await fetch(
      `${apiBasePath}/api/source-code?address=${Implementation}&chainId=${chainId}`,
    );

    const implData: ContractResponse = await res.json();
    const { ABI: implAbi, ContractName: implName } = implData.result[0];

    return {
      abi: JSON.parse(ABI),
      name: ContractName,
      implementation: {
        address: Implementation,
        abi: JSON.parse(implAbi),
        name: implName,
      },
    };
  } else {
    return { abi: JSON.parse(ABI), name: ContractName };
  }
};

export const fetchContractAbi = async ({
  address,
  chainId,
}: {
  address: string;
  chainId: number;
}): Promise<{
  abi: Interface;
  name: string;
}> => {
  const { abi, name, implementation } = await fetchContractAbiRaw({
    address,
    chainId,
  });

  if (implementation) {
    return { abi: implementation.abi, name: implementation.name };
  } else {
    return { abi, name };
  }
};

export const startHexWith0x = (hexValue?: string): Hex => {
  return hexValue
    ? hexValue.startsWith('0x')
      ? hexValue === '0x'
        ? '0x'
        : (hexValue as Hex)
      : `0x${hexValue}`
    : '0x';
};

import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { DataSource, ILike, Repository } from 'typeorm';
import { Bundler } from './entities/bundler.entity';
import { CommonMessage, AppMessage } from '../CommonMessages/CommonMessages';
import {
  CreateBundlerDto,
  FetchAllBundlerDto,
  FetchSingleBundlerDto,
  UpdateBundlerDto,
} from './dto/bundler.dto';
import { Application } from '../application/entities/application.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { paginate } from '../utils/common.service';

@Injectable()
export class BundlerService {
  private readonly logger = new Logger(BundlerService.name, {
    timestamp: true,
  });
  constructor(
    @InjectRepository(Application)
    private readonly applicationRepository: Repository<Application>,
    @InjectRepository(Bundler)
    private readonly bundlerRepository: Repository<Bundler>,
    private readonly dataSource: DataSource,
  ) {}

  async setupBundler(userId: number, payload: CreateBundlerDto) {
    try {
      const app = await this.applicationRepository.findOne({
        where: { id: payload.appId, user: { id: userId } },
      });
      if (!app) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppNotFound(),
        };
      }
      if (!app.isActive) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppIsInactive,
        };
      }
      const bundler = await this.bundlerRepository.findOne({
        where: { app: { id: payload.appId } },
      });
      if (bundler) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AlreadySetup('bundler'),
        };
      }
      const newBundler = this.bundlerRepository.create({
        bundlerName: payload.name,
        app: { id: payload.appId },
      });
      await this.bundlerRepository.save(newBundler);
      return {
        error: false,
        statusCode: HttpStatus.CREATED,
        message: AppMessage.AppCreated('Bundler'),
      };
    } catch (error) {
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async updateBundler(userId: number, updateBundlerDto: UpdateBundlerDto) {
    try {
      let responseMessage = null;
      const bundler = await this.dataSource
        .getRepository(Bundler)
        .createQueryBuilder('bundler')
        .innerJoinAndSelect('bundler.app', 'app', 'app.id = :id', {
          id: updateBundlerDto.appId,
        })
        .innerJoin('app.user', 'user', 'user.id = :userId', {
          userId: userId,
        })
        .getOne();
      if (!bundler) {
        return {
          error: true,
          statusCode: HttpStatus.NOT_FOUND,
          message: AppMessage.AppNotFound('Bundler'),
        };
      }
      if (!bundler.app.isActive) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppIsInactive,
        };
      }
      if (
        updateBundlerDto.bundlerName &&
        updateBundlerDto.bundlerName === bundler.bundlerName
      ) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.SameValues,
        };
      }

      if (updateBundlerDto.bundlerName !== undefined) {
        responseMessage = `Bundler updated`;
        bundler.bundlerName = updateBundlerDto.bundlerName;
      }

      if (updateBundlerDto.isActive !== undefined) {
        if (bundler.isActive === updateBundlerDto.isActive) {
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: AppMessage.AppAlreadySetSame('isActive is'),
          };
        }
        responseMessage = `Bundler ${updateBundlerDto.isActive ? 'active' : 'inactive'}`;
        bundler.isActive = updateBundlerDto.isActive;
      }

      await this.bundlerRepository.save(bundler);

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: responseMessage
          ? AppMessage.AppUpdated(responseMessage)
          : AppMessage.AppUpdated('Bundler updated'),
      };
    } catch (error) {
      this.logger.error(
        'Error in catch at updateBundler app:',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async getSingleBundler(userId: number, query: FetchSingleBundlerDto) {
    try {
      const bundler = await this.dataSource
        .getRepository(Bundler)
        .createQueryBuilder('bundler')
        .innerJoinAndSelect('bundler.app', 'app', 'app.id = :id', {
          id: query.appId,
        })
        .innerJoin('app.user', 'user', 'user.id = :userId', {
          userId: userId,
        })
        .getOne();
      if (!bundler) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: CommonMessage.NoDataFound,
        };
      }
      return {
        error: false,
        statusCode: HttpStatus.ACCEPTED,
        message: AppMessage.ServiceFetched('Bundler'),
        result: bundler,
      };
    } catch (error) {
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async getAllBundlers(userId: number, query: FetchAllBundlerDto) {
    try {
      let whereClause: any = {};

      if (query.s) {
        whereClause = {
          ...whereClause,
          bundlerName: ILike(`%${query.s}%`),
        };
      }
      const skip = paginate(query.page, query.limit);

      const bundler = await this.dataSource
        .getRepository(Bundler)
        .createQueryBuilder('bundler')
        .innerJoinAndSelect('bundler.app', 'app', 'app.userId = :id', {
          id: userId,
        })
        .where(whereClause)
        .skip(skip)
        .take(query.limit)
        .orderBy('bundler.createdAt', 'DESC')
        .getManyAndCount();

      return {
        error: false,
        statusCode: HttpStatus.ACCEPTED,
        message:
          bundler[1] > 0
            ? AppMessage.ServiceFetched('Bundlers')
            : CommonMessage.NoDataFound,
        result: bundler[0],
        totalCount: bundler[1],
      };
    } catch (error) {
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  // async deleteApp(userId: number, query: DeleteAppDto) {
  //   try {
  //     const app = await this.applicationRepository.findOne({
  //       where: { id: query.id, user: { id: userId } },
  //       relations: {
  //         user: true,
  //       },
  //     });
  //     if (!app) {
  //       return {
  //         error: true,
  //         statusCode: HttpStatus.NOT_FOUND,
  //         message: AppMessage.AppNotFound(),
  //       };
  //     }
  //   } catch (error) {
  //     console.log('error', error);
  //   }
  // }
}

import { Bundler } from '../../bundler/entities/bundler.entity';
import { User } from '../../user/entities/user.entity';
import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToOne,
  OneToMany,
} from 'typeorm';
import {
  Paymaster,
  SmartContract,
} from '../../paymaster/entities/paymaster.entity';
import { Origins } from '../../origins/entities/origins.entity';
import { Transaction } from '../../transaction/entity/transaction.entity';
import { AppRelayer } from 'src/appRelayer/entities/appRelayer.entity';
import { ChatAi } from '../../chatAi/entities/chatAi.entity';

@Entity()
export class Application {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: false })
  appName: string;

  @Column({ nullable: false })
  appDescription: string;

  @Column({ nullable: false })
  chainId: number;

  @Column({ nullable: false, default: '' })
  apiKey: string;

  @Column({ nullable: false, default: true })
  isActive: boolean;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;

  @OneToOne(() => Bundler, (bundler) => bundler.app)
  bundler: Bundler;

  @OneToOne(() => Paymaster, (paymaster) => paymaster.app)
  paymaster: Paymaster;

  @OneToOne(() => AppRelayer, (appRelayer) => appRelayer.app)
  relayer: AppRelayer;

  @OneToOne(() => ChatAi, (chatAi) => chatAi.app)
  chatAi: ChatAi;

  @ManyToOne(() => User, (user) => user.applications, {
    cascade: ['insert', 'update', 'remove'],
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  user: User;

  @OneToMany(() => SmartContract, (contract) => contract.app)
  smartContracts: SmartContract[];

  @OneToMany(() => Transaction, (transaction) => transaction.app)
  transactions: Transaction[];

  @OneToMany(() => Origins, (origin) => origin.app)
  origins: Origins[];
}

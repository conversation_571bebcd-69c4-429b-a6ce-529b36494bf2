import {
  IsNotEmpty,
  <PERSON><PERSON><PERSON>al,
  <PERSON>String,
  IsN<PERSON>ber,
  Matches,
  MaxLength,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Regexs } from '../utils/constants';

export class GateWayConsumerCustomId {
  @ApiProperty({
    description: 'Unique ID of the application',
    maxLength: 255,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  @Matches(Regexs.OnlyAlphaNumericWithSpace)
  customId: string;
}

export class GateWayServiceName {
  @ApiProperty({
    description: 'Unique service of the application',
    maxLength: 255,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  @Matches(Regexs.textWithoutSpace)
  serviceName: string;
}

export class GateWayConsumerUsername {
  @ApiProperty({
    description: 'Unique name of the application',
    maxLength: 255,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  @Matches(Regexs.textWithoutSpace)
  username: string;

  apiKey?: string;
}

export class GateWayConsumerRateLimit {
  @ApiProperty({
    description: 'Unique name of the application',
    maxLength: 255,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  @Matches(Regexs.OnlyAlphaNumericWithSpace)
  username: string;

  @ApiProperty({
    description: 'Plan name of the application',
    maxLength: 255,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  plan: string;

  // @ApiProperty({
  //   description: 'Unique name of the application',
  //   maxLength: 255,
  // })
  // @IsOptional()
  // @IsNotEmpty()
  // @IsString()
  // @MaxLength(255)
  // @Matches(Regexs.textWithoutSpace)
  // service: string;

  @ApiProperty({
    description: 'The number of HTTP request that can be made per day',
  })
  @IsOptional()
  @IsNotEmpty()
  @IsNumber()
  day: number;

  @ApiProperty({
    description: 'The number of HTTP request that can be made per second',
  })
  @IsOptional()
  @IsNotEmpty()
  @IsNumber()
  second: number;
}

export class AddGatewayConsumerDto extends GateWayConsumerUsername {
  @ApiProperty({
    description: 'Unique ID of the application',
    maxLength: 255,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  @Matches(Regexs.OnlyAlphaNumericWithSpace)
  customId: string;
}

export class GateWayConsumerApiKey {
  @ApiProperty({
    description: 'Unique name of the application',
    maxLength: 255,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  @Matches(Regexs.OnlyAlphaNumericWithSpace)
  username: string;

  @ApiProperty({
    description: 'Unique apikey of the Consumer',
    maxLength: 255,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  @Matches(Regexs.textWithoutSpace)
  apiKey: string;
}

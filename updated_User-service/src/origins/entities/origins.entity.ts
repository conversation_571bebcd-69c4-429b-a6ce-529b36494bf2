import { Application } from '../../application/entities/application.entity';
import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  Join<PERSON><PERSON>umn,
  CreateDateColumn,
  ManyToOne,
} from 'typeorm';
export enum OriginType {
  APP = 'app',
  BUNDLER = 'bundler',
  PAYMASTER = 'paymaster',
  RELAYER = 'relayer',
  CHATAI = 'chatai',
}
@Entity()
export class Origins {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false })
  originUrl: string;

  @Column({
    type: 'enum',
    default: OriginType.APP,
    enum: OriginType,
  })
  type: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @ManyToOne(() => Application, (app) => app.origins, {
    cascade: ['insert', 'update', 'remove'],
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  app: Application;
}

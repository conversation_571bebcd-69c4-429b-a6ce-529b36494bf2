import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { Origins } from './entities/origins.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Application } from '../application/entities/application.entity';
import { AppMessage, CommonMessage } from '../CommonMessages/CommonMessages';
import {
  FetchOriginsDto,
  InputOriginDto,
  OriginType,
  OriginTypeDto,
  RemoveOriginDto,
} from './dto/origins.dto';
import { Bundler } from '../bundler/entities/bundler.entity';
// import { RemoveAppConfigDto } from '../paymaster/dto/paymaster.dto';
import { Paymaster } from '../paymaster/entities/paymaster.entity';
import { paginate } from '../utils/common.service';
import { AppRelayer } from 'src/appRelayer/entities/appRelayer.entity';
import { ChatAi } from '../chatAi/entities/chatAi.entity';

@Injectable()
export class OriginsService {
  private readonly logger = new Logger(OriginsService.name, {
    timestamp: true,
  });
  constructor(
    @InjectRepository(Origins)
    private readonly originsRepository: Repository<Origins>,
    private readonly dataSource: DataSource,
  ) {}

  async getApplicationConfigByType(
    userId: number,
    config: OriginTypeDto,
    includeOrigin: boolean | number = false,
    includeSmartContracts: boolean | number = false,
    includeTransaction: boolean | number = false,
  ) {
    try {
      let originConfigQuery: SelectQueryBuilder<any> = null;
      let select = null;

      switch (config.type as string) {
        // case OriginType.APP:
        //   originConfigQuery = this.dataSource
        //     .getRepository(Application)
        //     .createQueryBuilder('app')
        //     .innerJoinAndSelect('app.user', 'user', 'user.id = :userId', {
        //       userId,
        //     });
        //   select = 'application';
        //   break;
        case OriginType.BUNDLER:
          originConfigQuery = this.dataSource
            .getRepository(Bundler)
            .createQueryBuilder('bundler')
            .innerJoinAndSelect('bundler.app', 'app', 'app.id = :id', {
              id: config.appId,
            })
            .innerJoinAndSelect('app.user', 'user', 'user.id = :userId', {
              userId,
            });
          select = 'Bundler';
          break;
        case OriginType.PAYMASTER:
          originConfigQuery = this.dataSource
            .getRepository(Paymaster)
            .createQueryBuilder('paymaster')
            .innerJoinAndSelect('paymaster.app', 'app', 'app.id = :id', {
              id: config.appId,
            })
            .innerJoinAndSelect('app.user', 'user', 'user.id = :userId', {
              userId,
            });
          select = 'Paymaster';
          break;
        case OriginType.RELAYER:
          originConfigQuery = this.dataSource
            .getRepository(AppRelayer)
            .createQueryBuilder('relayer')
            .innerJoinAndSelect('relayer.app', 'app', 'app.id = :id', {
              id: config.appId,
            })
            .innerJoinAndSelect('app.user', 'user', 'user.id = :userId', {
              userId,
            });
          select = 'Relayer';
          break;
        case OriginType.CHATAI:
          originConfigQuery = this.dataSource
            .getRepository(ChatAi)
            .createQueryBuilder('chatai')
            .innerJoinAndSelect('chatai.app', 'app', 'app.id = :id', {
              id: config.appId,
            })
            .innerJoinAndSelect('app.user', 'user', 'user.id = :userId', {
              userId,
            });
          select = 'ChatAi';
          break;
        // Add more cases as needed for different origin types
        default:
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: 'Invalid origin type',
          };
      }

      if (includeOrigin && typeof includeOrigin == 'boolean') {
        originConfigQuery.leftJoinAndSelect(
          'app.origins',
          'origins',
          'origins.type =:type',
          {
            type: config.type,
          },
        );
      }

      if (includeOrigin && typeof includeOrigin == 'number') {
        originConfigQuery.leftJoinAndSelect(
          'app.origins',
          'origins',
          'origins.id =:originId AND origins.type =:type',
          { originId: includeOrigin, type: config.type },
        );
      }

      if (includeSmartContracts && typeof includeSmartContracts == 'boolean') {
        originConfigQuery.leftJoinAndSelect(
          'app.smartContracts',
          'smartContracts',
        );
      }

      if (includeTransaction && typeof includeTransaction == 'boolean') {
        originConfigQuery.leftJoinAndSelect('app.transactions', 'transactions');
      }

      // Execute the query
      const isConfigValid = await originConfigQuery.getOne();

      this.logger.verbose('isConfigValid', isConfigValid);

      // Handle the case where the config is not found
      if (!isConfigValid) {
        return {
          error: true,
          statusCode: HttpStatus.NOT_FOUND,
          message: AppMessage.AppNotFound(select),
        };
      }

      // Return success response
      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: AppMessage.AppFetched,
        data: isConfigValid,
      };
    } catch (error) {
      // Handle unexpected errors
      this.logger.error(
        'Error in catch at getApplicationConfigByType:',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async configureOriginByType(userId: number, config: InputOriginDto) {
    try {
      const getConfig = await this.getApplicationConfigByType(userId, config);
      if (getConfig.error) {
        return getConfig;
      }
      const originCount = await this.originsRepository.count({
        where: {
          app: { id: config.appId },
        },
      });
      if (originCount >= 10) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.OriginLimitExceed,
        };
      }
      const origin = await this.originsRepository.findOne({
        where: {
          originUrl: config.origin,
          app: { id: config.appId },
        },
        relations: {
          app: true,
        },
      });
      if (origin) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.OriginAlreadyAdded,
        };
      }
      const data = {
        originUrl: config.origin,
        app: { id: config.appId },
        type: config.type,
      };
      await this.originsRepository.save(data);

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: AppMessage.OriginAdded,
      };
    } catch (error) {
      this.logger.error(
        'Error in catch at configureOriginByType :',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async removeOriginConfig(
    userId: number,
    removeAppConfigDto: RemoveOriginDto,
  ) {
    try {
      const getConfig = await this.getApplicationConfigByType(
        userId,
        { appId: removeAppConfigDto.appId, type: removeAppConfigDto.type },
        removeAppConfigDto.originId,
      );
      if (getConfig.error) {
        return getConfig;
      }
      if (
        !getConfig.data['app']['origins'] ||
        !getConfig.data['app']['origins'].length
      ) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.OriginNotFound,
        };
      }

      await this.originsRepository.delete(removeAppConfigDto.originId);

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: AppMessage.FeatureRemoved(),
      };
    } catch (error) {
      this.logger.error(
        'Error in catch at remove origin:',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async getUserOriginsList(userId: number, query: FetchOriginsDto) {
    try {
      const whereClause: any = {
        type: query.type,
      };

      const skip = paginate(query.page, query.limit);

      const origins = await this.dataSource
        .getRepository(Origins)
        .createQueryBuilder('origin')
        .innerJoinAndSelect(
          'origin.app',
          'app',
          'app.userId = :id AND app.id = :appId',
          {
            id: userId,
            appId: query.appId,
          },
        )
        .select('origin')
        .where(whereClause)
        .skip(skip)
        .take(query.limit)
        .orderBy('origin.createdAt', 'DESC')
        .getManyAndCount();

      return {
        error: false,
        statusCode: HttpStatus.ACCEPTED,
        message:
          origins[1] > 0
            ? AppMessage.ServiceFetched('Origins')
            : CommonMessage.NoDataFound,
        result: origins[0],
        totalCount: origins[1],
      };
    } catch (error) {
      this.logger.error(
        'Error in catch at getUserOriginsList',
        JSON.stringify(error),
      );

      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }
}

<p align="center">
  <a href="http://nestjs.com/" target="blank"><img src="https://www.abstraxn.com/wp-content/uploads/2024/06/new-logo.png" width="250" 
   alt="Nest Logo" /></a>
</p>


Abstraxn is a platform enabling the seamless integration and deployment of Smart Accounts and facilitating user operations are essential features in any toolkit tailored for ERC4337.


## Abstraxn User Service

It is a Backend Server to manage the user service of Abstraxn.
This server handles the platform's user authentication and activities. 



## Dependencies
Node.js,
Express,
MongoDB,
rabbitMQ,
Socket.io



## Installation

Install user-service with npm

npm version: 10.8.1^

node version: 20.12.2^

```bash
  npm install
```
    
## How to run locally
Install dependencies

```bash
  npm install
```

Start the server

```bash
  npm run start
```
## Deployment

To deploy this project run

```bash
  npm run start:prod
```

# Environment Variables

Make a file .env
```bash
  touch .env
```

To run this project, you will need to add the following environment variables to your .env file

`PORT=3000`

### Secret JWT
`JWT_SECRET=<jwt-secret>`

`PASSWORD=<encryption-password>`

### PostGres
`POSTGRES_HOST=<host-url>`

`POSTGRES_PORT=5432`

`POSTGRES_USER=<host-user-name>`

`POSTGRES_PASSWORD=<host-user-password>`

`POSTGRES_DB=abstraxn`

### RabbitMq
`RMQ_URL=<rabbitmq-url>`

### Mail
`PROJECT_NAME=abstraxn`

`SMTP_HOST=<smtp-host>`

`SMTP_POST=587`

`SMTP_USER=<smtp-user>`

`SMTP_PASS=<smtp-password>`

`SMTP_FROM=<smtp-admin-email>`

`FRONTEND_URL=<user-frontend-ui-url>`

### Gateway
`GATEWAY_URL=<gateway-url>`

### LANDING PAGE INFO
`LANDING_URL=<landing-page-url>`

### BlockExplorer API keys
`ETHERSCAN=<etherscan-api-key>`

`POLYGONSCAN=<polygonscan-api-key>`

`ZKEVMPOLYGONSCAN=<zkevmpolygonscan-api-key>`

`BSCSCAN=<bscscan-api-key>`

### origin
`PAYMASTER_ORIGIN=<paymaster-api-url>`

`BUNDLER_ORIGIN=<bundler-api-url>`

### Socker Service
`API_URL=<user-backend-api-url>`
